// Augment API Token Tool - Background Script
// 处理跨域请求和OAuth流程管理

const clientID = "v";

// 工具函数
function base64URLEncode(buffer) {
    return btoa(String.fromCharCode.apply(null, new Uint8Array(buffer)))
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
}

async function sha256Hash(input) {
    const encoder = new TextEncoder();
    const data = encoder.encode(input);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    return hashBuffer;
}

async function createOAuthState() {
    // 生成随机字节
    const codeVerifierArray = new Uint8Array(32);
    crypto.getRandomValues(codeVerifierArray);
    const codeVerifier = base64URLEncode(codeVerifierArray.buffer);

    // 创建 code challenge
    const codeChallenge = base64URLEncode(await sha256Hash(codeVerifier));

    // 生成随机 state
    const stateArray = new Uint8Array(8);
    crypto.getRandomValues(stateArray);
    const state = base64URLEncode(stateArray.buffer);

    const oauthState = {
        codeVerifier,
        codeChallenge,
        state,
        creationTime: Date.now()
    };

    // 存储状态以供后续使用
    await chrome.storage.local.set({ oauthState: JSON.stringify(oauthState) });

    return oauthState;
}

function generateAuthorizeURL(oauthState) {
    const params = new URLSearchParams({
        response_type: "code",
        code_challenge: oauthState.codeChallenge,
        client_id: clientID,
        state: oauthState.state,
        prompt: "login",
    });

    return `https://auth.augmentcode.com/authorize?${params.toString()}`;
}

// 解析授权码函数
function parseCode(code) {
    try {
        const parsed = JSON.parse(code);
        if (!parsed.code || !parsed.tenant_url) {
            throw new Error("缺少必要的code或tenant_url字段");
        }
        return {
            code: parsed.code,
            state: parsed.state,
            tenant_url: parsed.tenant_url
        };
    } catch (e) {
        throw new Error("JSON解析失败: " + e.message);
    }
}

async function getAccessToken(tenant_url, codeVerifier, code) {
    // 确保tenant_url以/结尾
    if (!tenant_url.endsWith('/')) {
        tenant_url = tenant_url + '/';
    }

    console.log("正在请求token...");
    console.log("URL:", `${tenant_url}token`);
    console.log("codeVerifier:", codeVerifier);
    console.log("code:", code);

    const data = {
        grant_type: "authorization_code",
        client_id: clientID,
        code_verifier: codeVerifier,
        redirect_uri: "",
        code: code,
    };

    console.log("请求数据:", JSON.stringify(data));

    try {
        const response = await fetch(`${tenant_url}token`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(data)
        });

        console.log("API响应状态:", response.status);
        const responseText = await response.text();
        console.log("API响应内容:", responseText);

        const json = JSON.parse(responseText);
        console.log("解析后的响应:", json);

        if (json.access_token) {
            return json.access_token;
        } else {
            throw new Error(`No access token found in response. Full response: ${JSON.stringify(json)}`);
        }
    } catch (error) {
        console.error("请求错误:", error);
        throw new Error(`Request error: ${error}`);
    }
}

// 消息处理
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log("Background收到消息:", request);
    
    if (request.action === "createOAuthState") {
        createOAuthState().then(sendResponse);
        return true; // 异步响应
    }
    
    if (request.action === "generateAuthorizeURL") {
        const url = generateAuthorizeURL(request.oauthState);
        sendResponse({ url });
    }
    
    if (request.action === "parseCode") {
        try {
            const result = parseCode(request.code);
            sendResponse({ success: true, result });
        } catch (error) {
            sendResponse({ success: false, error: error.message });
        }
    }
    
    if (request.action === "getAccessToken") {
        getAccessToken(request.tenant_url, request.codeVerifier, request.code)
            .then(token => sendResponse({ success: true, token }))
            .catch(error => sendResponse({ success: false, error: error.message }));
        return true; // 异步响应
    }
    
    if (request.action === "getStoredState") {
        chrome.storage.local.get(['oauthState']).then(result => {
            sendResponse({ oauthState: result.oauthState });
        });
        return true; // 异步响应
    }
    
    if (request.action === "saveAccessToken") {
        chrome.storage.local.set({ access_token: request.token }).then(() => {
            sendResponse({ success: true });
        });
        return true; // 异步响应
    }
});

// 扩展图标点击事件
chrome.action.onClicked.addListener((tab) => {
    // 发送消息到content script显示菜单
    chrome.tabs.sendMessage(tab.id, { action: "showMenu" });
});
