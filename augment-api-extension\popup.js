// Augment API Token Tool - Popup Script

document.addEventListener('DOMContentLoaded', function() {
    const startAuthBtn = document.getElementById('startAuth');
    const processJsonBtn = document.getElementById('processJson');
    const openCurrentTabBtn = document.getElementById('openCurrentTab');
    const statusDiv = document.getElementById('status');

    // 显示状态消息
    function showStatus(message, type = 'info') {
        statusDiv.textContent = message;
        statusDiv.className = `status ${type}`;
        statusDiv.style.display = 'block';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            statusDiv.style.display = 'none';
        }, 3000);
    }

    // 启动认证流程
    startAuthBtn.addEventListener('click', async () => {
        try {
            showStatus('正在启动认证流程...', 'info');
            
            // 创建OAuth状态
            const oauthState = await chrome.runtime.sendMessage({ action: "createOAuthState" });
            
            // 生成授权URL
            const urlResponse = await chrome.runtime.sendMessage({ 
                action: "generateAuthorizeURL", 
                oauthState: oauthState 
            });
            
            // 在新标签页中打开授权URL
            chrome.tabs.create({ url: urlResponse.url });
            
            showStatus('已打开授权页面，请完成登录', 'success');
            
            // 关闭popup
            window.close();
            
        } catch (error) {
            console.error('启动认证失败:', error);
            showStatus('启动认证失败: ' + error.message, 'error');
        }
    });

    // 处理JSON信息
    processJsonBtn.addEventListener('click', async () => {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // 发送消息到content script显示JSON处理界面
            chrome.tabs.sendMessage(tab.id, { action: "showJsonProcessingUI" });
            
            showStatus('已在页面中显示JSON处理界面', 'success');
            
            // 关闭popup
            window.close();
            
        } catch (error) {
            console.error('显示JSON处理界面失败:', error);
            showStatus('操作失败: ' + error.message, 'error');
        }
    });

    // 在当前标签页操作
    openCurrentTabBtn.addEventListener('click', async () => {
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // 检查是否在支持的域名
            const supportedDomains = ['augmentcode.com', 'cursor.com', 'windsurf.com'];
            const isSupported = supportedDomains.some(domain => tab.url.includes(domain));
            
            if (!isSupported) {
                showStatus('请在支持的网站上使用此功能', 'error');
                return;
            }
            
            // 发送消息到content script显示菜单
            chrome.tabs.sendMessage(tab.id, { action: "showMenu" });
            
            showStatus('已在页面中显示操作菜单', 'success');
            
            // 关闭popup
            window.close();
            
        } catch (error) {
            console.error('显示菜单失败:', error);
            showStatus('操作失败: ' + error.message, 'error');
        }
    });

    // 检查当前标签页状态
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            const currentUrl = tabs[0].url;
            const supportedDomains = ['augmentcode.com', 'cursor.com', 'windsurf.com'];
            const isSupported = supportedDomains.some(domain => currentUrl.includes(domain));
            
            if (isSupported) {
                openCurrentTabBtn.style.display = 'block';
                openCurrentTabBtn.textContent = '在当前页面操作';
            } else {
                openCurrentTabBtn.style.display = 'none';
            }
        }
    });
});
