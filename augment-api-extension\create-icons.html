<!DOCTYPE html>
<html>
<head>
    <title>创建扩展图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-preview { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #4285f4; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h2>Augment API Token Tool - 图标生成器</h2>
    <p>点击下面的按钮生成并下载扩展图标：</p>
    
    <div class="icon-preview">
        <h3>图标预览：</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <canvas id="icon48" width="48" height="48"></canvas>
        <canvas id="icon128" width="128" height="128"></canvas>
    </div>
    
    <button onclick="generateIcons()">生成图标</button>
    <button onclick="downloadIcon(16)">下载 16x16</button>
    <button onclick="downloadIcon(48)">下载 48x48</button>
    <button onclick="downloadIcon(128)">下载 128x128</button>
    
    <div id="instructions" style="margin-top: 20px; padding: 15px; background: #f0f0f0; border-radius: 5px;">
        <h3>使用说明：</h3>
        <ol>
            <li>点击"生成图标"按钮</li>
            <li>分别点击三个下载按钮</li>
            <li>将下载的文件重命名为：icon16.png, icon48.png, icon128.png</li>
            <li>将文件放入 augment-api-extension/icons/ 文件夹</li>
            <li>恢复 manifest.json 中的图标配置</li>
        </ol>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制背景圆形
            ctx.fillStyle = '#4285f4';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制字母 A
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('A', size/2, size/2);
        }
        
        function generateIcons() {
            const sizes = [16, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`icon${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 页面加载时自动生成图标
        window.onload = generateIcons;
    </script>
</body>
</html>
