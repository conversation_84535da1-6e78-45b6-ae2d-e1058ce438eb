# Augment API Token Tool - Chrome Extension

这是一个Chrome扩展版本的Augment API令牌获取工具，功能与原Tampermonkey脚本完全一致。

## 功能特性

- 🔐 完整的OAuth 2.0 PKCE认证流程
- 🌐 支持多个平台：augmentcode.com、cursor.com、windsurf.com
- 🎯 自动化令牌获取和处理
- 📋 一键复制tenant_url和token
- 🎨 美观的浮动UI界面
- 🔄 智能页面监控和菜单恢复

## 安装方法

### 方法一：开发者模式安装（推荐）

1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择 `augment-api-extension` 文件夹
6. 扩展安装完成！

### 方法二：打包安装

1. 在扩展管理页面点击"打包扩展程序"
2. 选择 `augment-api-extension` 文件夹
3. 生成 `.crx` 文件
4. 拖拽 `.crx` 文件到扩展管理页面安装

## 使用方法

### 方式一：使用扩展弹窗

1. 点击浏览器工具栏中的扩展图标
2. 在弹窗中点击"1. 登录获取授权 JSON"
3. 完成授权后，点击"2. 处理 JSON 信息"
4. 粘贴授权码JSON并处理

### 方式二：使用页面浮动菜单

1. 访问支持的网站（augmentcode.com、cursor.com、windsurf.com）
2. 页面右下角会自动显示蓝色的"A"按钮
3. 点击按钮显示操作菜单
4. 按照菜单提示完成操作

## 详细流程

### 1. 启动认证流程
- 点击"登录获取授权 JSON"
- 系统会自动跳转到授权页面
- 完成登录和授权

### 2. 处理授权信息
- 点击"处理 JSON 信息"
- 在文本框中粘贴从授权页面复制的JSON
- 点击"处理授权码"

### 3. 获取令牌
- 系统自动将授权码换取访问令牌
- 成功后会显示可拖动的凭证浮窗
- 浮窗包含tenant_url和token，可一键复制

## 权限说明

扩展需要以下权限：

- `activeTab`: 访问当前标签页内容
- `storage`: 本地存储OAuth状态和令牌
- `scripting`: 在页面中注入脚本
- `clipboardWrite`: 复制功能
- `host_permissions`: 访问指定域名进行API请求

## 文件结构

```
augment-api-extension/
├── manifest.json          # 扩展配置文件
├── background.js          # 后台脚本（处理API请求）
├── content.js            # 内容脚本（页面UI交互）
├── popup.html            # 扩展弹窗界面
├── popup.js              # 弹窗脚本
├── icons/                # 扩展图标
│   ├── icon16.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # 说明文档
```

## 与Tampermonkey版本的区别

### 优势
- ✅ 无需安装Tampermonkey
- ✅ 更好的权限管理
- ✅ 更稳定的运行环境
- ✅ 可发布到Chrome Web Store
- ✅ 原生Chrome扩展体验

### API替换
- `GM_setValue/GM_getValue` → `chrome.storage.local`
- `GM_xmlhttpRequest` → `fetch API`
- `GM_setClipboard` → `navigator.clipboard.writeText`
- `GM_registerMenuCommand` → 扩展弹窗和右键菜单

## 故障排除

### 扩展无法加载
- 确保开启了开发者模式
- 检查manifest.json语法是否正确
- 查看扩展管理页面的错误信息

### 页面菜单不显示
- 确认当前网站是否为支持的域名
- 检查浏览器控制台是否有错误
- 尝试刷新页面

### 令牌获取失败
- 检查网络连接
- 确认授权码JSON格式正确
- 查看浏览器控制台的详细错误信息

## 开发说明

如需修改扩展功能：

1. 修改相应的JavaScript文件
2. 在扩展管理页面点击"重新加载"
3. 测试修改后的功能

## 版本历史

- **v2.0** - Chrome扩展版本
  - 完整移植Tampermonkey脚本功能
  - 添加扩展弹窗界面
  - 优化用户体验

## 许可证

MIT License

## 作者

backgroundPower

---

如有问题或建议，请通过GitHub Issues反馈。
