# 安装和使用指南

## 快速安装

### 1. 准备工作
确保您的Chrome浏览器版本在88以上（支持Manifest V3）。

### 2. 安装步骤

1. **下载扩展文件**
   - 将整个 `augment-api-extension` 文件夹保存到本地

2. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击页面右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择 `augment-api-extension` 文件夹
   - 点击"选择文件夹"

5. **确认安装**
   - 扩展列表中出现"Augment API Token Tool"
   - 浏览器工具栏出现扩展图标

## 使用方法

### 方法一：扩展弹窗操作

1. **点击扩展图标**
   - 在浏览器工具栏找到扩展图标
   - 点击打开操作弹窗

2. **开始认证流程**
   - 点击"1. 登录获取授权 JSON"
   - 会自动打开新标签页进行授权

3. **处理授权信息**
   - 完成授权后，点击"2. 处理 JSON 信息"
   - 在页面中粘贴授权码JSON

### 方法二：页面浮动菜单

1. **访问支持的网站**
   - augmentcode.com
   - cursor.com  
   - windsurf.com

2. **使用浮动菜单**
   - 页面右下角会出现蓝色"A"按钮
   - 点击显示操作菜单
   - 按提示完成操作

## 功能验证

### 检查扩展是否正常工作

1. **访问测试页面**
   ```
   https://augmentcode.com
   ```

2. **查看页面右下角**
   - 应该出现蓝色圆形"A"按钮
   - 点击按钮应该显示菜单

3. **测试弹窗功能**
   - 点击扩展图标
   - 弹窗应该正常显示

### 常见问题排查

**扩展无法加载**
- 检查文件夹结构是否完整
- 确认manifest.json文件存在
- 查看扩展管理页面的错误信息

**页面菜单不显示**
- 确认当前网站域名是否支持
- 检查浏览器控制台错误信息
- 尝试刷新页面

**权限问题**
- 确认扩展已启用
- 检查是否被浏览器安全策略阻止

## 完整使用流程示例

### 获取Augment API令牌的完整步骤

1. **准备阶段**
   ```
   1. 安装Chrome扩展
   2. 访问 augmentcode.com
   3. 确认页面右下角出现"A"按钮
   ```

2. **认证阶段**
   ```
   1. 点击"A"按钮 → 选择"1. 登录获取授权 JSON"
   2. 在新打开的页面完成登录
   3. 复制页面显示的JSON授权码
   ```

3. **令牌获取阶段**
   ```
   1. 返回原页面，点击"A"按钮
   2. 选择"2. 处理 JSON 信息"
   3. 粘贴JSON授权码，点击"处理授权码"
   4. 等待系统处理并显示令牌
   ```

4. **使用令牌**
   ```
   1. 在弹出的凭证浮窗中复制tenant_url和token
   2. 在您的应用中使用这些凭证
   3. 浮窗可拖动，可随时复制信息
   ```

## 高级功能

### 自动化特性
- 自动检测授权页面并点击复制按钮
- 智能页面监控，菜单被移除时自动恢复
- 支持单页应用的路由变化检测

### 数据管理
- OAuth状态自动存储和恢复
- 令牌本地缓存
- 安全的状态验证机制

### 用户体验
- 可拖动的凭证显示浮窗
- 一键复制功能
- 友好的错误提示和状态反馈

## 卸载方法

如需卸载扩展：

1. 打开 `chrome://extensions/`
2. 找到"Augment API Token Tool"
3. 点击"移除"按钮
4. 确认删除

## 技术支持

如遇到问题：

1. 检查浏览器控制台错误信息
2. 确认网络连接正常
3. 尝试重新加载扩展
4. 查看README.md中的故障排除部分

---

**注意**：此扩展与原Tampermonkey脚本功能完全一致，只是运行环境不同。如果您之前使用过Tampermonkey版本，使用方法基本相同。
