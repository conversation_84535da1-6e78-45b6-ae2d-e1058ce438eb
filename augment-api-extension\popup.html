<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            margin: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h2 {
            margin: 0;
            color: #4285f4;
            font-size: 18px;
        }
        
        .header p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 12px;
        }
        
        .button {
            width: 100%;
            padding: 12px;
            margin: 8px 0;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .button-primary {
            background: #4285f4;
            color: white;
        }
        
        .button-primary:hover {
            background: #3367d6;
        }
        
        .button-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #dadce0;
        }
        
        .button-secondary:hover {
            background: #e8eaed;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .divider {
            height: 1px;
            background: #dadce0;
            margin: 15px 0;
        }
        
        .instructions {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
            margin-top: 10px;
        }
        
        .instructions ol {
            margin: 5px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 3px 0;
        }
        
        .version {
            text-align: center;
            font-size: 10px;
            color: #999;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>Augment API Token Tool</h2>
        <p>获取Augment API的访问令牌</p>
    </div>
    
    <button id="startAuth" class="button button-primary">
        1. 登录获取授权 JSON
    </button>
    
    <button id="processJson" class="button button-secondary">
        2. 处理 JSON 信息
    </button>
    
    <div class="divider"></div>
    
    <button id="openCurrentTab" class="button button-secondary">
        在当前标签页操作
    </button>
    
    <div id="status" class="status"></div>
    
    <div class="instructions">
        <strong>使用说明：</strong>
        <ol>
            <li>点击"登录获取授权 JSON"开始认证流程</li>
            <li>完成授权后，点击"处理 JSON 信息"</li>
            <li>粘贴授权码JSON并处理</li>
            <li>获取到的令牌会显示在浮窗中</li>
        </ol>
    </div>
    
    <div class="version">
        v2.0 - Chrome Extension
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
